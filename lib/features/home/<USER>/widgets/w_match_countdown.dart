import 'dart:async';
import 'package:flutter/material.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/home/<USER>/entities/match_entity.dart';

class WMatchCountdown extends StatefulWidget {
  const WMatchCountdown({
    super.key,
    required this.match,
    this.textStyle,
    this.backgroundColor,
    this.textColor,
    this.padding,
  });

  final MatchEntity match;
  final TextStyle? textStyle;
  final Color? backgroundColor;
  final Color? textColor;
  final EdgeInsets? padding;

  @override
  State<WMatchCountdown> createState() => _WMatchCountdownState();
}

class _WMatchCountdownState extends State<WMatchCountdown> {
  Timer? _timer;
  String _countdownText = '';

  @override
  void initState() {
    super.initState();
    _updateCountdown();
    _startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        _updateCountdown();
      }
    });
  }

  void _updateCountdown() {
    if (widget.match.start_date == null) {
      setState(() {
        _countdownText = '';
      });
      return;
    }

    final now = DateTime.now();
    final matchStart = widget.match.start_date!;
    final difference = matchStart.difference(now);

    // If match has already started or finished, show appropriate message
    if (difference.isNegative) {
      // Check if match started within the last 90 minutes
      final timeSinceStart = now.difference(matchStart);
      if (timeSinceStart.inMinutes <= 90) {
        setState(() {
          _countdownText = "O'yin boshlandi";
        });
      } else {
        // Match is likely finished, don't show countdown
        setState(() {
          _countdownText = '';
        });
      }
      return;
    }

    // Calculate time components
    final days = difference.inDays;
    final hours = difference.inHours % 24;
    final minutes = difference.inMinutes % 60;

    // Format countdown text in Uzbek
    String countdownText = '';
    
    if (days > 0) {
      countdownText += '$days kun';
      if (hours > 0) {
        countdownText += ' $hours soat';
      }
    } else if (hours > 0) {
      countdownText += '$hours soat';
      if (minutes > 0) {
        countdownText += ' $minutes daqiqa';
      }
    } else if (minutes > 0) {
      countdownText += '$minutes daqiqa';
    } else {
      countdownText = 'Tez orada boshlanadi';
    }

    setState(() {
      _countdownText = countdownText;
    });
  }

  @override
  Widget build(BuildContext context) {
    // Don't show countdown for finished matches
    if (widget.match.match_status == "match_finished" || _countdownText.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: widget.padding ?? const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        _countdownText,
        style: widget.textStyle ?? 
          context.textTheme.bodySmall!.copyWith(
            color: widget.textColor ?? AppColors.primary,
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
        textAlign: TextAlign.center,
      ),
    );
  }
}

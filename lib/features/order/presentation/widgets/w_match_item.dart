// ignore_for_file: deprecated_member_use

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/routes/app_routes.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/home/<USER>/entities/match_entity.dart';
import 'package:echipta/features/home/<USER>/widgets/w_match_status.dart';
import 'package:echipta/features/home/<USER>/widgets/w_match_countdown.dart';
import 'package:echipta/features/order/presentation/bloc/order_bloc.dart';
import 'package:echipta/features/order/presentation/widgets/w_stadium.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter/cupertino.dart';

class WMatchItem extends StatelessWidget {
  const WMatchItem({super.key, required this.item, this.readOnly = false});

  final MatchEntity item;
  final bool? readOnly;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (!readOnly!) {
          if (item.match_status != "match_finished") {
            context.read<OrderBloc>().add(
              const SelectOrderTypeEvent(orderType: OrderType.ticket),
            );
            context.read<OrderBloc>().add(SelectMatchEvent(match: item));
            context.push(AppRouter.sector, extra: item);
          } else {
            Fluttertoast.showToast(msg: "O'yin yakunlangan", fontSize: 18);
          }
        }
      },
      child: Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.primary.withOpacity(0.2)),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Left team - flexible space
                Expanded(
                  flex: 1,
                  child: Column(
                    children: [
                      CachedNetworkImage(
                        imageUrl: item.main_team.image,
                        width: 40,
                        height: 40,
                        errorWidget:
                            (context, url, error) =>
                                const Icon(Icons.sports_soccer, size: 40),
                      ),
                      const Gap(4),
                      Text(
                        item.main_team.name,
                        style: context.textTheme.bodySmall!.copyWith(
                          color: AppColors.primary,
                          fontWeight: FontWeight.w700,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                // Center container - always perfectly centered
                Container(
                  width: 100,
                  height: 80, // Fixed height to prevent vertical shifting
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        if (item.match_status == "match_finished") ...[
                          Text(
                            "${item.team1_score}:${item.team2_score}",
                            style: context.textTheme.displayLarge!.copyWith(
                              fontWeight: FontWeight.w700,
                              color: AppColors.primary,
                            ),
                          ),
                          WMatchStatus(item: item),
                        ] else ...[
                          Text(
                            DateFormat('HH:mm').format(item.start_date!),
                            style: context.textTheme.displayLarge!.copyWith(
                              fontWeight: FontWeight.w700,
                              color: AppColors.primary,
                            ),
                          ),
                          WMatchStatus(item: item),
                        ],
                      ],
                    ),
                  ),
                ),
                // Right team - flexible space
                Expanded(
                  flex: 1,
                  child: Column(
                    children: [
                      CachedNetworkImage(
                        imageUrl: item.second_team.image,
                        width: 40,
                        height: 40,
                        errorWidget:
                            (context, url, error) =>
                                const Icon(Icons.sports_soccer, size: 40),
                      ),
                      const Gap(4),
                      Text(
                        item.second_team.name,
                        style: context.textTheme.bodySmall!.copyWith(
                          color: AppColors.primary,
                          fontWeight: FontWeight.w700,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const Gap(10),
            GestureDetector(
              onTap: () {
                showModalBottomSheet(
                  context: context,
                  showDragHandle: true,
                  isScrollControlled: true,
                  backgroundColor: AppColors.white,
                  useRootNavigator: true,
                  elevation: 0,
                  builder: (context) {
                    final stadium = item.match_stadium;
                    return WStadium(stadium: stadium);
                  },
                );
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(CupertinoIcons.location_solid, size: 18),
                  Text(
                    "${item.match_stadium.name}  | ${DateFormat("dd-MMMM", context.locale.languageCode).format(item.start_date!)}",
                  ),
                ],
              ),
            ),
            const Gap(8),
            // Countdown timer at the bottom
            WMatchCountdown(
              match: item,
              textStyle: context.textTheme.bodySmall!.copyWith(
                fontSize: 11,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

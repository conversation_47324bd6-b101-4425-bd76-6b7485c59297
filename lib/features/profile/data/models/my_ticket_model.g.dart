// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'my_ticket_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MyTicketModel _$MyTicketModelFromJson(Map<String, dynamic> json) =>
    MyTicketModel(
      ticket_id: (json['ticket_id'] as num?)?.toInt() ?? 0,
      sector: json['sector'] as String? ?? "",
      row: (json['row'] as num?)?.toInt() ?? 0,
      seat: (json['seat'] as num?)?.toInt() ?? 0,
      match:
          json['match'] == null
              ? const MatchEntity()
              : const MatchConverter().fromJson(
                json['match'] as Map<String, dynamic>,
              ),
      start_date: json['start_date'] as String? ?? "",
      price: (json['price'] as num?)?.toInt() ?? 0,
      is_gifted: json['is_gifted'] as bool? ?? false,
    );

Map<String, dynamic> _$MyTicketModelToJson(MyTicketModel instance) =>
    <String, dynamic>{
      'ticket_id': instance.ticket_id,
      'sector': instance.sector,
      'row': instance.row,
      'seat': instance.seat,
      'match': const MatchConverter().toJson(instance.match),
      'start_date': instance.start_date,
      'price': instance.price,
      'is_gifted': instance.is_gifted,
    };

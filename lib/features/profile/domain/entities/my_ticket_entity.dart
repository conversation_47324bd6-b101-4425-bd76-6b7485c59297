// ignore_for_file: public_member_api_docs, sort_constructors_first
// ignore_for_file: non_constant_identifier_names

import 'package:echipta/features/profile/data/models/my_ticket_model.dart';
import 'package:equatable/equatable.dart';

import 'package:echipta/features/home/<USER>/entities/match_entity.dart';
import 'package:json_annotation/json_annotation.dart';

class MyTicketEntity extends Equatable {
  final int ticket_id;
  final String sector;
  final int row;
  final int seat;
  @MatchConverter()
  final MatchEntity match;
  final String start_date;
  final int price;
  final bool is_gifted;

  const MyTicketEntity({
    this.ticket_id = 0,
    this.sector = "",
    this.row = 0,
    this.seat = 0,
    this.match = const MatchEntity(),
    this.start_date = "",
    this.price = 0,
    this.is_gifted = false,
  });

  @override
  List<Object?> get props => [
        ticket_id,
        sector,
        row,
        seat,
        match,
        start_date,
        price,
        is_gifted,
      ];

  MyTicketEntity copyWith({
    int? ticket_id,
    String? sector,
    int? row,
    int? seat,
    MatchEntity? match,
    String? start_date,
    int? amount,
    bool? is_gifted,
  }) {
    return MyTicketEntity(
      ticket_id: ticket_id ?? this.ticket_id,
      sector: sector ?? this.sector,
      row: row ?? this.row,
      seat: seat ?? this.seat,
      match: match ?? this.match,
      start_date: start_date ?? this.start_date,
      price: amount ?? this.price,
      is_gifted: is_gifted ?? this.is_gifted,
    );
  }
}

class MyTicketConverter
    extends JsonConverter<MyTicketEntity, Map<String, dynamic>> {
  @override
  MyTicketEntity fromJson(Map<String, dynamic> json) =>
      MyTicketModel.fromJson(json);

  @override
  Map<String, dynamic> toJson(MyTicketEntity object) => {};

  const MyTicketConverter();
}

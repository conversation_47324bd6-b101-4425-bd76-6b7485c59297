import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:echipta/assets/app_assets.dart';
import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/core/utils/extentions.dart';
import 'package:echipta/features/profile/domain/entities/my_ticket_entity.dart';
import 'package:echipta/generated/locale_keys.g.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:ticketcher/ticketcher.dart';

class WMyTicketsItem extends StatelessWidget {
  const WMyTicketsItem({super.key, required this.item});

  final MyTicketEntity item;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Gap(16),
        Ticketcher(
          decoration: TicketcherDecoration(
            borderRadius: TicketRadius(radius: 16),
            shadow: BoxShadow(
              offset: Offset(0, 4),
              blurRadius: 4,
              spreadRadius: 5,
              color: AppColors.black.withOpacity(0.25),
            ),
            divider: TicketDivider.dashed(
              color: AppColors.primary,
              thickness: 1.0,
              dashWidth: 5.0,
              dashSpace: 4.0,
            ),
          ),
          width: double.maxFinite,
          sections: [
            Section(
              child: Column(
                children: [
                  Gap(10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CachedNetworkImage(
                        imageUrl: item.match.main_team.image,
                        width: 40,
                        height: 40,
                        errorWidget: (context, url, error) {
                          return SvgPicture.asset(AppAssets.team, color: AppColors.primary);
                        },
                      ),
                      const Gap(8),
                      Text(
                        "vs",
                        style: context.textTheme.bodyLarge!.copyWith(
                          color: AppColors.primary,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      const Gap(8),
                      CachedNetworkImage(
                        imageUrl: item.match.second_team.image,
                        width: 40,
                        height: 40,
                        errorWidget: (context, url, error) {
                          return SvgPicture.asset(AppAssets.team, color: AppColors.primary);
                        },
                      ),
                      const Gap(15),
                      SizedBox(
                        height: 60,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  LocaleKeys.sector.tr(),
                                  style: context.textTheme.bodyMedium!.copyWith(
                                    color: AppColors.primary,
                                  ),
                                ),
                                Text(
                                  item.sector.toUpperCase(),
                                  style: context.textTheme.bodySmall!.copyWith(
                                    color: AppColors.primary,
                                    fontWeight: FontWeight.w900,
                                    fontSize: 20,
                                  ),
                                ),
                              ],
                            ),
                            const VerticalDivider(),
                            Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  LocaleKeys.row.tr(),
                                  style: context.textTheme.bodyMedium!.copyWith(
                                    color: AppColors.primary,
                                  ),
                                ),
                                Text(
                                  item.row.toString().toUpperCase(),
                                  style: context.textTheme.bodySmall!.copyWith(
                                    color: AppColors.primary,
                                    fontWeight: FontWeight.w900,
                                    fontSize: 20,
                                  ),
                                ),
                              ],
                            ),
                            const VerticalDivider(),
                            Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  LocaleKeys.seat.tr(),
                                  style: context.textTheme.bodyMedium!.copyWith(
                                    color: AppColors.primary,
                                  ),
                                ),
                                Text(
                                  item.seat.toString().toUpperCase(),
                                  style: context.textTheme.bodySmall!.copyWith(
                                    color: AppColors.primary,
                                    fontWeight: FontWeight.w900,
                                    fontSize: 20,
                                  ),
                                ),
                              ],
                            ),
                            const Gap(20),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  item.is_gifted ? "Sovg'a qilingan" : "Sotib olingan",
                                  style: context.textTheme.labelSmall!.copyWith(
                                    color: AppColors.red,
                                    fontWeight: FontWeight.w800,
                                    fontSize: 10,
                                    letterSpacing: 0.3,
                                  ),
                                ),
                                item.match.start_date != null
                                    ? Column(
                                      mainAxisSize: MainAxisSize.min,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          DateFormat('dd MM yyyy').format(
                                            item.match.start_date?.toLocal() ??
                                                DateTime.now(),
                                          ),
                                          style: context.textTheme.bodyMedium!
                                              .copyWith(
                                                color: AppColors.primary,
                                                fontWeight: FontWeight.w600,
                                                letterSpacing: 0.3,
                                              ),
                                        ),
                                        Text(
                                          DateFormat('HH:mm').format(
                                            item.match.start_date?.toLocal() ??
                                                DateTime.now(),
                                          ),
                                          style: context.textTheme.bodyLarge!
                                              .copyWith(
                                                color: AppColors.primary,
                                                fontWeight: FontWeight.w600,
                                                letterSpacing: 0.3,
                                              ),
                                        ),
                                      ],
                                    )
                                    : const Text("-"),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  Gap(10),
                ],
              ),
            ),
            Section(
              child: Padding(
                padding: const EdgeInsets.all(15.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "O'zbekiston Superligasi",
                          style: context.textTheme.headlineMedium!.copyWith(
                            color: AppColors.primary,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 0.5,
                          ),
                        ),
                        Text(
                          "${item.price.toDouble().formatAsSpaceSeparated()} so'm",
                          style: context.textTheme.headlineMedium!.copyWith(
                            color: AppColors.primary,
                            fontWeight: FontWeight.w600,
                            letterSpacing: 0.5,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        const Gap(16),
      ],
    );
  }

  Widget _buildSeatInfoItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: AppColors.white.withOpacity(0.7), size: 16),
        const Gap(4),
        Text(
          label,
          style: context.textTheme.bodySmall!.copyWith(
            color: AppColors.white.withOpacity(0.8),
            fontSize: 11,
            fontWeight: FontWeight.w400,
          ),
        ),
        const Gap(2),
        Text(
          value,
          style: context.textTheme.bodyMedium!.copyWith(
            color: AppColors.white,
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
        ),
      ],
    );
  }
}

part of 'profile_bloc.dart';

sealed class ProfileEvent extends Equatable {
  const ProfileEvent();

  @override
  List<Object> get props => [];
}

class GetMeEvent extends ProfileEvent {}

class GetIdCardEvent extends ProfileEvent {}

class GetMyTicketsEvent extends ProfileEvent {}

class SelectIdCardTypeEvent extends ProfileEvent {
  final IdCardTypeEnum type;

  const SelectIdCardTypeEvent({required this.type});
}

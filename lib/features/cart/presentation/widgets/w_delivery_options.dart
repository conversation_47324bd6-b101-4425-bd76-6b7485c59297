import 'package:echipta/assets/app_colors.dart';
import 'package:echipta/features/cart/domain/entities/order_entity.dart';
import 'package:echipta/features/cart/presentation/bloc/cart_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';

class WDeliveryOptions extends StatelessWidget {
  const WDeliveryOptions({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CartBloc, CartState>(
      builder: (context, state) {
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: AppColors.dark.withOpacity(0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Yetkazib berish turi',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.dark,
                ),
              ),
              const Gap(16),

              // Delivery Type Options
              Column(
                children: [
                  _buildDeliveryOption(
                    context,
                    DeliveryType.inStore,
                    'Olib ketish',
                    'Do\'kondan olib ketish',
                    Icons.store_outlined,
                    state.deliveryType == DeliveryType.inStore,
                  ),
                  const Gap(12),
                  _buildDeliveryOption(
                    context,
                    DeliveryType.delivery,
                    'Yetkazib berish',
                    'Manzilga yetkazib berish',
                    Icons.delivery_dining_outlined,
                    state.deliveryType == DeliveryType.delivery,
                  ),
                ],
              ),

              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Delivery Address Input (always present but conditionally visible)
                  const Gap(16),
                  const Divider(color: AppColors.mediumGrey),
                  const Gap(16),
                  const Text(
                    'Yetkazib berish manzili',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: AppColors.dark,
                    ),
                  ),
                  const Gap(8),
                  state.deliveryType == DeliveryType.inStore
                      ? Text(
                        "Andijon viloyati, Yaxshi MFY, Bobur arena o`yingohi",
                      )
                      : TextField(
                        enabled: state.deliveryType == DeliveryType.delivery,
                        onChanged: (value) {
                          if (state.deliveryType == DeliveryType.delivery) {
                            context.read<CartBloc>().add(
                              SetDeliveryAddress(address: value),
                            );
                          }
                        },
                        decoration: InputDecoration(
                          hintText: 'Yetkazib berish manzilini kiriting...',
                          hintStyle: const TextStyle(
                            color: AppColors.grey,
                            fontSize: 14,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: const BorderSide(
                              color: AppColors.mediumGrey,
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: const BorderSide(
                              color: AppColors.primary,
                            ),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 12,
                          ),
                        ),
                      ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDeliveryOption(
    BuildContext context,
    DeliveryType type,
    String title,
    String subtitle,
    IconData icon,
    bool isSelected,
  ) {
    return GestureDetector(
      onTap: () {
        context.read<CartBloc>().add(SetDeliveryType(deliveryType: type));
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? AppColors.primary.withOpacity(0.1)
                  : AppColors.mediumGrey,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? AppColors.primary : AppColors.mediumGrey,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: isSelected ? AppColors.primary : AppColors.grey,
              size: 24,
            ),
            const Gap(12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: isSelected ? AppColors.primary : AppColors.dark,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: isSelected ? AppColors.primary : AppColors.grey,
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              const Icon(
                Icons.check_circle,
                color: AppColors.primary,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:echipta/features/home/<USER>/widgets/w_match_countdown.dart';
import 'package:echipta/features/home/<USER>/entities/match_entity.dart';
import 'package:echipta/features/auth/domain/entities/item_entity.dart';
import 'package:echipta/features/home/<USER>/entities/match_stadium_entity.dart';

void main() {
  group('WMatchCountdown Widget Tests', () {
    testWidgets('should not display countdown for finished matches', (WidgetTester tester) async {
      // Create a finished match
      final finishedMatch = MatchEntity(
        id: 1,
        title: "Test Match",
        start_date: DateTime.now().subtract(const Duration(hours: 2)),
        match_status: "match_finished",
        main_team: const ItemEntity(id: 1, name: "Team A"),
        second_team: const ItemEntity(id: 2, name: "Team B"),
        match_stadium: const MatchStadiumEntity(id: 1, name: "Test Stadium"),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WMatchCountdown(match: finishedMatch),
          ),
        ),
      );

      // Should not display anything for finished matches
      expect(find.byType(WMatchCountdown), findsOneWidget);
      expect(find.byType(Container), findsNothing);
    });

    testWidgets('should display countdown for upcoming matches', (WidgetTester tester) async {
      // Create an upcoming match (2 hours from now)
      final upcomingMatch = MatchEntity(
        id: 1,
        title: "Test Match",
        start_date: DateTime.now().add(const Duration(hours: 2)),
        match_status: "match_not_started",
        main_team: const ItemEntity(id: 1, name: "Team A"),
        second_team: const ItemEntity(id: 2, name: "Team B"),
        match_stadium: const MatchStadiumEntity(id: 1, name: "Test Stadium"),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WMatchCountdown(match: upcomingMatch),
          ),
        ),
      );

      // Should display countdown container
      expect(find.byType(WMatchCountdown), findsOneWidget);
      expect(find.byType(Container), findsOneWidget);
      
      // Should contain countdown text
      expect(find.textContaining('soat'), findsOneWidget);
    });

    testWidgets('should display "O\'yin boshlandi" for recently started matches', (WidgetTester tester) async {
      // Create a match that started 30 minutes ago (within 90 minutes)
      final recentlyStartedMatch = MatchEntity(
        id: 1,
        title: "Test Match",
        start_date: DateTime.now().subtract(const Duration(minutes: 30)),
        match_status: "match_started",
        main_team: const ItemEntity(id: 1, name: "Team A"),
        second_team: const ItemEntity(id: 2, name: "Team B"),
        match_stadium: const MatchStadiumEntity(id: 1, name: "Test Stadium"),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WMatchCountdown(match: recentlyStartedMatch),
          ),
        ),
      );

      // Should display "O'yin boshlandi"
      expect(find.byType(WMatchCountdown), findsOneWidget);
      expect(find.text("O'yin boshlandi"), findsOneWidget);
    });

    testWidgets('should display days and hours for matches far in future', (WidgetTester tester) async {
      // Create a match that's 2 days and 3 hours from now
      final futureMatch = MatchEntity(
        id: 1,
        title: "Test Match",
        start_date: DateTime.now().add(const Duration(days: 2, hours: 3)),
        match_status: "match_not_started",
        main_team: const ItemEntity(id: 1, name: "Team A"),
        second_team: const ItemEntity(id: 2, name: "Team B"),
        match_stadium: const MatchStadiumEntity(id: 1, name: "Test Stadium"),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WMatchCountdown(match: futureMatch),
          ),
        ),
      );

      // Should display days and hours
      expect(find.byType(WMatchCountdown), findsOneWidget);
      expect(find.textContaining('kun'), findsOneWidget);
      expect(find.textContaining('soat'), findsOneWidget);
    });

    testWidgets('should not display countdown when start_date is null', (WidgetTester tester) async {
      // Create a match with null start_date
      const matchWithNullDate = MatchEntity(
        id: 1,
        title: "Test Match",
        start_date: null,
        match_status: "match_not_started",
        main_team: ItemEntity(id: 1, name: "Team A"),
        second_team: ItemEntity(id: 2, name: "Team B"),
        match_stadium: MatchStadiumEntity(id: 1, name: "Test Stadium"),
      );

      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: WMatchCountdown(match: matchWithNullDate),
          ),
        ),
      );

      // Should not display anything when start_date is null
      expect(find.byType(WMatchCountdown), findsOneWidget);
      expect(find.byType(Container), findsNothing);
    });
  });
}
